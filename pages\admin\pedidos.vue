<template>
  <div class="min-h-screen bg-gray-100">
    <HeaderAdmin />
    <main class="container mx-auto px-4 py-8">
      <div class="bg-white shadow-md rounded-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-gray-800">Gerenciamento de Pedidos</h1>

        <!-- Filtros e busca -->
        <div class="mb-6 flex flex-wrap gap-4">
          <div class="relative flex-grow max-w-md">
            <input v-model="searchQuery" type="text" placeholder="Buscar por ID, cliente ou produto"
              class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              @input="filterOrders" />
            <i class="fas fa-search absolute right-3 top-3 text-gray-400"></i>
          </div>

          <div>
            <select v-model="statusFilter"
              class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              @change="filterOrders">
              <option value="all">Todos os status</option>
              <option value="pending">Aguardando pagamento</option>
              <option value="paid">Pago</option>
              <option value="shipped">Enviado</option>
              <option value="delivered">Entregue</option>
              <option value="canceled">Cancelado</option>
            </select>
          </div>

          <div>
            <select v-model="sortOption"
              class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              @change="sortOrders">
              <option value="date_desc">Data (Nova-Antiga)</option>
              <option value="date_asc">Data (Antiga-Nova)</option>
              <option value="total_desc">Valor (Maior-Menor)</option>
              <option value="total_asc">Valor (Menor-Maior)</option>
            </select>
          </div>
        </div>

        <!-- Tabela de pedidos -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID do Pedido
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor Total
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Itens</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <template v-if="loading">
                <OrderRowSkeleton v-for="i in 5" :key="i" />
              </template>
              <tr v-else-if="paginatedOrders.length === 0" class="text-center">
                <td colspan="7" class="px-6 py-4 text-gray-500">Nenhum pedido encontrado</td>
              </tr>
              <tr v-for="order in paginatedOrders" :key="order.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="font-medium">#{{ order.id.substring(0, 8) }}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ order.customerName }}</div>
                      <div class="text-sm text-gray-500">{{ order.customerEmail }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ formatDate(order.createdAt) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex flex-col space-y-1">
                    <span :class="getStatusClass(order.status)">
                      {{ getStatusText(order.status) }}
                    </span>
                    <span v-if="order.trackCode" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      <i class="fas fa-truck mr-1"></i> Rastreável
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatCurrency(order.total) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ order.items?.length || 0 }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex flex-wrap gap-2">
                    <button @click="viewOrderDetails(order.id)" class="text-blue-600 hover:text-blue-900">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button @click="updateOrderStatus(order)" class="text-green-600 hover:text-green-900">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button v-if="order.trackCode" @click="trackOrder(order)" class="text-blue-600 hover:text-blue-900">
                      <i class="fas fa-truck"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Paginação -->
        <div class="mt-6 flex justify-between items-center">
          <div class="text-sm text-gray-500">
            Mostrando {{ paginatedOrders.length }} de {{ filteredOrders.length }} pedidos
          </div>
          <div class="flex space-x-2">
            <button @click="prevPage" :disabled="currentPage === 1"
              class="px-4 py-2 border rounded-md bg-white text-gray-700 disabled:opacity-50">
              Anterior
            </button>
            <button @click="nextPage" :disabled="currentPage >= totalPages"
              class="px-4 py-2 border rounded-md bg-white text-gray-700 disabled:opacity-50">
              Próxima
            </button>
          </div>
        </div>
      </div>

      <!-- Modal de detalhes do pedido -->
      <div v-if="selectedOrder" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div class="p-6">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-xl font-bold text-gray-800">Detalhes do Pedido #{{ selectedOrder.id.substring(0, 8) }}
              </h2>
              <button @click="selectedOrder = null" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 class="text-lg font-semibold mb-2">Informações do Cliente</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                  <p><span class="font-medium">Nome:</span> {{ selectedOrder.customerName }}</p>
                  <p><span class="font-medium">Email:</span> {{ selectedOrder.customerEmail }}</p>
                  <p><span class="font-medium">Telefone:</span> {{ formatPhone(selectedOrder.customerPhone) }}</p>
                  <p><span class="font-medium">CPF:</span> {{ formatCPF(selectedOrder.customerCPF) }}</p>
                </div>
              </div>

              <div>
                <h3 class="text-lg font-semibold mb-2">Informações do Pedido</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                  <p><span class="font-medium">Data:</span> {{ formatDate(selectedOrder.createdAt) }}</p>
                  <p><span class="font-medium">Status:</span> <span :class="getStatusClass(selectedOrder.status)">{{
                    getStatusText(selectedOrder.status) }}</span></p>
                  <p><span class="font-medium">Método de Pagamento:</span> {{ selectedOrder.paymentMethod }}</p>
                  <p><span class="font-medium">Total:</span> {{ formatCurrency(selectedOrder.total) }}</p>
                </div>
              </div>
            </div>

            <div class="mb-6">
              <h3 class="text-lg font-semibold mb-2">Endereço de Entrega</h3>
              <div class="bg-gray-50 p-4 rounded-lg">
                <p>{{ selectedOrder.shippingAddress?.street }}, {{ selectedOrder.shippingAddress?.number }}</p>
                <p>{{ selectedOrder.shippingAddress?.complement }}</p>
                <p>{{ selectedOrder.shippingAddress?.neighborhood }}, {{ selectedOrder.shippingAddress?.city }} - {{
                  selectedOrder.shippingAddress?.state }}</p>
                <p>CEP: {{ formatCEP(selectedOrder.shippingAddress?.zipcode || '') }}</p>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-semibold mb-2">Itens do Pedido</h3>
              <div class="bg-gray-50 p-4 rounded-lg">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Produto</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Quantidade</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Preço Unit.</th>
                      <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Subtotal</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200">
                    <tr v-for="(item, index) in selectedOrder.items" :key="index">
                      <td class="px-4 py-2">
                        <div class="flex items-center">
                          <img :src="item.image" :alt="item.name" class="h-10 w-10 object-cover rounded mr-2" />
                          <span>{{ item.name }}</span>
                        </div>
                      </td>
                      <td class="px-4 py-2">{{ item.quantity }}</td>
                      <td class="px-4 py-2">{{ formatCurrency(item.price) }}</td>
                      <td class="px-4 py-2">{{ formatCurrency(item.price * item.quantity) }}</td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colspan="3" class="px-4 py-2 text-right font-medium">Subtotal:</td>
                      <td class="px-4 py-2">{{ formatCurrency(calculateSubtotal(selectedOrder.items)) }}</td>
                    </tr>
                    <tr>
                      <td colspan="3" class="px-4 py-2 text-right font-medium">Frete:</td>
                      <td class="px-4 py-2">{{ formatCurrency(selectedOrder.shipping) }}</td>
                    </tr>
                    <tr v-if="selectedOrder.discount">
                      <td colspan="3" class="px-4 py-2 text-right font-medium">Desconto:</td>
                      <td class="px-4 py-2 text-red-600">-{{ formatCurrency(selectedOrder.discount) }}</td>
                    </tr>
                    <tr>
                      <td colspan="3" class="px-4 py-2 text-right font-bold">Total:</td>
                      <td class="px-4 py-2 font-bold">{{ formatCurrency(selectedOrder.total) }}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            <!-- Código de Rastreamento -->
            <div class="mb-6 mt-6">
              <h3 class="text-lg font-semibold mb-2">Código de Rastreamento</h3>
              <div class="bg-gray-50 p-4 rounded-lg">
                <div v-if="!editingTrackCode" class="flex flex-wrap justify-between items-center gap-2">
                  <div class="flex-grow">
                    <p v-if="selectedOrder.trackCode" class="text-sm font-medium break-all">{{ selectedOrder.trackCode }}</p>
                    <p v-else class="text-sm text-gray-500 italic">Nenhum código de rastreamento informado</p>
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <button v-if="selectedOrder.trackCode" @click="trackOrder(selectedOrder)"
                      class="px-3 py-1 bg-green-100 text-green-600 hover:bg-green-200 rounded text-sm font-medium flex-shrink-0">
                      <i class="fas fa-truck mr-1"></i> Rastrear
                    </button>
                    <button @click="startEditTrackCode"
                      class="px-3 py-1 bg-blue-100 text-blue-600 hover:bg-blue-200 rounded text-sm font-medium flex-shrink-0">
                      {{ selectedOrder.trackCode ? 'Editar' : 'Adicionar' }}
                    </button>
                  </div>
                </div>
                <div v-else class="space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Código de Rastreamento</label>
                    <input v-model="trackCodeInput" type="text" placeholder="Digite o código de rastreamento"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                  </div>
                  <div class="flex flex-wrap justify-end gap-2">
                    <button @click="cancelEditTrackCode"
                      class="px-3 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                      Cancelar
                    </button>
                    <button @click="saveTrackCode"
                      class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                      Salvar
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="mt-6 flex justify-end space-x-4">
              <button @click="updateOrderStatus(selectedOrder)"
                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Atualizar Status
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal de atualização de status -->
      <div v-if="showStatusModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
          <div class="p-6">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-xl font-bold text-gray-800">Atualizar Status do Pedido</h2>
              <button @click="showStatusModal = false" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>

            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">Status atual:</label>
              <span :class="getStatusClass(orderToUpdate.status)" class="inline-block px-3 py-1">
                {{ getStatusText(orderToUpdate.status) }}
              </span>
            </div>

            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">Novo status:</label>
              <select v-model="newStatus"
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="pending">Aguardando pagamento</option>
                <option value="paid">Pago</option>
                <option value="shipped">Enviado</option>
                <option value="delivered">Entregue</option>
                <option value="canceled">Cancelado</option>
                <option value="concluída">Concluída</option>
              </select>
            </div>

            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">Observação (opcional):</label>
              <textarea v-model="statusNote" rows="3"
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Adicione uma observação sobre a mudança de status..."></textarea>
            </div>

            <div class="flex justify-end space-x-4">
              <button @click="showStatusModal = false"
                class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                Cancelar
              </button>
              <button @click="saveStatusUpdate"
                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                :disabled="isUpdating">
                <span v-if="isUpdating" class="flex items-center">
                  <div class="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                  Salvando...
                </span>
                <span v-else>Salvar</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal de Rastreamento -->
      <div v-if="showTrackingModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div class="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <div class="sticky top-0 bg-white p-6 border-b flex justify-between items-center z-10">
            <h2 class="text-xl font-bold">Rastreamento de Encomenda</h2>
            <button @click="showTrackingModal = false" class="text-gray-500 hover:text-gray-800">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="p-6">
            <div v-if="trackingLoading" class="flex justify-center items-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>

            <div v-else-if="trackingError" class="bg-red-50 p-4 rounded-lg text-red-700 mb-4">
              <p class="font-medium">Erro ao rastrear encomenda</p>
              <p>{{ trackingError }}</p>
              <div class="mt-4">
                <button @click="window.open(`https://rastreamento.correios.com.br/app/index.php?objeto=${selectedOrder?.trackCode?.trim()}`, '_blank')"
                  class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                  Rastrear no site dos Correios
                </button>
              </div>
            </div>

            <div v-else-if="trackingInfo">
              <div class="mb-4 bg-gray-50 p-4 rounded-lg">
                <div class="flex justify-between items-center mb-2">
                  <h3 class="font-semibold">Código de Rastreamento</h3>
                  <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">{{ selectedOrder?.trackCode }}</span>
                </div>

                <div v-if="trackingInfo.codigo" class="text-sm">
                  <p><span class="font-medium">Serviço:</span> {{ trackingInfo.servico || 'Correios' }}</p>
                  <p v-if="trackingInfo.ultimo?.data"><span class="font-medium">Última atualização:</span> {{ trackingInfo.ultimo.data }} às {{ trackingInfo.ultimo.hora }}</p>
                </div>
              </div>

              <h3 class="font-semibold mb-3">Histórico de Rastreamento</h3>

              <div v-if="trackingInfo.eventos && trackingInfo.eventos.length > 0" class="space-y-4">
                <div v-for="(evento, index) in trackingInfo.eventos" :key="index"
                  class="border-l-2 pl-4 pb-4"
                  :class="index === 0 ? 'border-green-500' : 'border-gray-300'">
                  <div class="flex justify-between items-start">
                    <div>
                      <p class="font-medium">{{ evento.status }}</p>
                      <p class="text-sm text-gray-600">{{ evento.data }} às {{ evento.hora }}</p>
                    </div>
                  </div>
                  <p class="text-sm mt-1">{{ evento.local }}</p>
                  <p v-if="evento.subStatus" class="text-sm text-gray-600 mt-1">{{ evento.subStatus }}</p>
                </div>
              </div>

              <div v-else class="bg-yellow-50 p-4 rounded-lg text-yellow-700">
                <p>Nenhuma informação de rastreamento disponível para este código.</p>
              </div>
            </div>

            <div class="mt-6 flex justify-end">
              <button @click="showTrackingModal = false"
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                Fechar
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useNuxtApp } from '#app'
import { useRoute } from 'vue-router'
import { doc, updateDoc, Timestamp, arrayUnion } from 'firebase/firestore'
import { useCustomerStore } from '~/stores/customerStore'
import { IOrder } from '~/types/customer'
import OrderRowSkeleton from '~/components/admin/OrderRowSkeleton.vue'

// Middleware para proteger a rota
definePageMeta({
  middleware: ['admin']
})

// Estado
const orders = ref<Order[]>([])
const filteredOrders = ref<Order[]>([])
const loading = ref(true)
const searchQuery = ref('')
const statusFilter = ref('all')
const sortOption = ref('date_desc')
const currentPage = ref(1)
const itemsPerPage = 10
const selectedOrder = ref<Order | null>(null)
const showStatusModal = ref(false)
const orderToUpdate = ref<Partial<Order>>({})
const newStatus = ref('')
const statusNote = ref('')
const isUpdating = ref(false)
const showTrackingModal = ref(false)
const trackingInfo = ref<any>(null)
const trackingLoading = ref(false)
const trackingError = ref('')
const editingTrackCode = ref(false)
const trackCodeInput = ref('')

// Firebase e Stores
const { $db } = useNuxtApp()
const route = useRoute()
const customerStore = useCustomerStore()

// Interface para tipagem
interface Order {
  id: string
  customerName: string
  customerEmail: string
  customerPhone?: string
  customerCPF?: string
  createdAt: any
  status: string
  total: number
  items: any[]
  paymentMethod?: string
  shipping?: number
  discount?: number
  shippingAddress?: {
    street: string
    number: string
    complement?: string
    neighborhood: string
    city: string
    state: string
    zipcode: string
  }
  userId?: string
  statusHistory?: any[]
  updatedAt?: any
  trackCode?: string // Código de rastreamento dos Correios
}

// Computed
const totalPages = computed(() => Math.ceil(filteredOrders.value.length / itemsPerPage))
const paginatedOrders = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredOrders.value.slice(start, end)
})

// Observar parâmetros da URL
watch(() => route.query.clientId, (newClientId) => {
  if (newClientId && typeof newClientId === 'string') {
    filterOrdersByClient(newClientId)
  } else {
    filteredOrders.value = [...orders.value]
  }
}, { immediate: true })

// Métodos
async function fetchOrders() {
  try {
    loading.value = true

    // Buscar todos os clientes através da store
    await customerStore.fetchAllCustomers()

    // Converter os pedidos dos clientes para o formato que a página espera
    const allCustomerOrders = customerStore.getAllOrders
    const ordersData: Order[] = allCustomerOrders.map(customerOrder => {
      // Encontrar o cliente dono deste pedido
      const customer = customerStore.getCustomerById(customerOrder.customerId)


      // Mapear do formato IOrder para o formato Order usado nesta página
      return {
        id: customerOrder.id,
        customerName: customer?.name || 'Cliente não encontrado',
        customerEmail: customer?.email || '',
        customerPhone: customer?.phone || '',
        customerCPF: customer?.cpf || '',
        createdAt: customerOrder.createdAt,
        status: customerOrder.collectionStatus || 'pending',
        total: 0, // Precisamos calcular isso com base nos itens
        items: [], // Precisamos obter os itens do pedido
        paymentMethod: customerOrder.paymentType || '',
        userId: customer?.id || '',
        updatedAt: customerOrder.updatedAt
      } as Order
    })

    console.log(`✅ Carregados ${ordersData.length} pedidos de clientes`)
    orders.value = ordersData

    // Aplicar filtros iniciais
    if (route.query.clientId) {
      const clientIdParam = route.query.clientId
      if (typeof clientIdParam === 'string') {
        filterOrdersByClient(clientIdParam)
      }
    } else {
      filteredOrders.value = [...orders.value]
    }

    sortOrders()
  } catch (error) {
    console.error('❌ Erro ao buscar pedidos:', error)
  } finally {
    loading.value = false
  }
}

function filterOrdersByClient(clientId: string) {
  filteredOrders.value = orders.value.filter(order => order.userId === clientId)
}

function filterOrders() {
  const query = searchQuery.value.toLowerCase()
  const status = statusFilter.value

  filteredOrders.value = orders.value.filter(order => {
    // Filtro por status
    if (status !== 'all' && order.status !== status) {
      return false
    }

    // Filtro por termo de busca
    if (query) {
      const searchFields = [
        order.id,
        order.customerName,
        order.customerEmail,
        order.customerPhone || '',
        order.customerCPF || ''
      ]

      return searchFields.some(field => field && field.toString().toLowerCase().includes(query))
    }

    return true
  })

  currentPage.value = 1
}

function sortOrders() {
  const option = sortOption.value

  filteredOrders.value = [...filteredOrders.value].sort((a, b) => {
    if (option === 'date_desc') {
      return (b.createdAt?.toDate?.() || 0) - (a.createdAt?.toDate?.() || 0)
    } else if (option === 'date_asc') {
      return (a.createdAt?.toDate?.() || 0) - (b.createdAt?.toDate?.() || 0)
    } else if (option === 'total_desc') {
      return (b.total || 0) - (a.total || 0)
    } else if (option === 'total_asc') {
      return (a.total || 0) - (b.total || 0)
    }
    return 0
  })
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

function viewOrderDetails(orderId: string) {
  const order = orders.value.find(o => o.id === orderId)
  if (order) {
    selectedOrder.value = { ...order }
  }
}

function updateOrderStatus(order: Order) {
  orderToUpdate.value = { ...order }
  newStatus.value = order.status || 'pending'
  statusNote.value = ''
  showStatusModal.value = true
}

async function saveStatusUpdate() {
  if (!orderToUpdate.value.id) return

  try {
    isUpdating.value = true

    // Encontrar o cliente e o pedido
    const orderResult = customerStore.getOrderById(orderToUpdate.value.id)

    if (!orderResult) {
      console.error(`❌ Pedido ${orderToUpdate.value.id} não encontrado`)
      return
    }

    const { order, customerId } = orderResult

    // Atualizar o status do pedido no Firestore
    const customerRef = doc($db, 'users', customerId)

    // Preparar histórico de status
    const statusUpdate = {
      status: newStatus.value,
      date: new Date(),
      note: statusNote.value || ''
    }

    // TODO: Implementar a atualização do pedido dentro do documento do cliente
    // Esta parte depende da estrutura exata de como os pedidos são armazenados
    // dentro do documento do cliente

    console.log(`✅ Status do pedido ${orderToUpdate.value.id} atualizado para ${newStatus.value}`)

    // Atualizar o pedido localmente
    const index = orders.value.findIndex(o => o.id === orderToUpdate.value.id)
    if (index !== -1) {
      orders.value[index].status = newStatus.value
      orders.value[index].updatedAt = new Date()

      // Atualizar o histórico de status
      if (!orders.value[index].statusHistory) {
        orders.value[index].statusHistory = []
      }
      orders.value[index].statusHistory.push(statusUpdate)

      // Se este é o pedido selecionado, atualizar também
      if (selectedOrder.value && selectedOrder.value.id === orderToUpdate.value.id) {
        selectedOrder.value.status = newStatus.value
        selectedOrder.value.updatedAt = new Date()

        if (!selectedOrder.value.statusHistory) {
          selectedOrder.value.statusHistory = []
        }
        selectedOrder.value.statusHistory.push({ ...statusUpdate })
      }
    }

    // Fechar o modal e limpar os dados
    showStatusModal.value = false
    orderToUpdate.value = {}
    newStatus.value = ''
    statusNote.value = ''

    // Reordenar e filtrar os pedidos
    filterOrders()
    sortOrders()

  } catch (error) {
    console.error('❌ Erro ao atualizar status:', error)
  } finally {
    isUpdating.value = false
  }
}

function formatCurrency(value: number | undefined): string {
  if (value === undefined) return 'R$ 0,00'
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

function formatDate(date: any): string {
  if (!date) return ''
  if (typeof date.toDate === 'function') {
    date = date.toDate()
  }
  return new Date(date).toLocaleDateString('pt-BR')
}

function formatCPF(cpf: string | undefined): string {
  if (!cpf) return ''
  return cpf.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, '$1.$2.$3-$4')
}

function formatCEP(cep: string | undefined): string {
  if (!cep) return ''
  return cep.replace(/^(\d{5})(\d{3})$/, '$1-$2')
}

function formatPhone(phone: string | undefined): string {
  if (!phone) return ''
  return phone.replace(/^(\d{2})(\d{5})(\d{4})$/, '($1) $2-$3')
}

function calculateSubtotal(items: any[]): number {
  return items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0)
}

function getStatusText(status: string): string {
  switch (status) {
    case 'pending': return 'Aguardando pagamento'
    case 'paid': return 'Pago'
    case 'shipped': return 'Enviado'
    case 'delivered': return 'Entregue'
    case 'canceled': return 'Cancelado'
    case 'concluída': return 'Concluída'
    default: return status
  }
}

function getStatusClass(status: string): string {
  const classes = 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full'

  switch (status) {
    case 'pending':
      return `${classes} bg-yellow-100 text-yellow-800`
    case 'paid':
      return `${classes} bg-green-100 text-green-800`
    case 'shipped':
      return `${classes} bg-blue-100 text-blue-800`
    case 'delivered':
      return `${classes} bg-purple-100 text-purple-800`
    case 'canceled':
      return `${classes} bg-red-100 text-red-800`
    case 'concluída':
      return `${classes} bg-emerald-100 text-emerald-800`
    default:
      return `${classes} bg-gray-100 text-gray-800`
  }
}

// Função para rastrear o pedido usando a API dos Correios
const trackOrder = async (order: Order) => {
  if (!order.trackCode) {
    alert('Este pedido não possui código de rastreamento.');
    return;
  }

  trackingLoading.value = true;
  trackingError.value = '';
  trackingInfo.value = null;
  showTrackingModal.value = true;

  try {
    // Usar a API Linketrack para rastreamento
    const trackCode = order.trackCode.trim();

    // Fazer a chamada à API através do servidor
    const response = await fetch(`/api/track?code=${trackCode}`);

    if (!response.ok) {
      throw new Error(`Erro ao rastrear: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.error) {
      // Fallback: abrir o site dos Correios em uma nova aba
      window.open(`https://rastreamento.correios.com.br/app/index.php?objeto=${trackCode}`, '_blank');
      throw new Error(data.error);
    }

    trackingInfo.value = data;
    console.log("Dados de rastreamento:", data);
  } catch (e: any) {
    console.error('Erro ao rastrear pedido:', e);
    trackingError.value = e.message || 'Ocorreu um erro ao rastrear o pedido. Por favor, tente novamente.';

    // Fallback: abrir o site dos Correios em uma nova aba
    if (order.trackCode) {
      window.open(`https://rastreamento.correios.com.br/app/index.php?objeto=${order.trackCode.trim()}`, '_blank');
    }
  } finally {
    trackingLoading.value = false;
  }
};

// Funções para gerenciar o código de rastreamento
const startEditTrackCode = () => {
  if (!selectedOrder.value) return;
  trackCodeInput.value = selectedOrder.value.trackCode || '';
  editingTrackCode.value = true;
};

const cancelEditTrackCode = () => {
  editingTrackCode.value = false;
};

const saveTrackCode = async () => {
  if (!selectedOrder.value) return;

  loading.value = true;

  try {
    // Encontrar o pedido na lista
    const orderToUpdate = orders.value.find(o => o.id === selectedOrder.value?.id);
    if (!orderToUpdate) {
      throw new Error('Pedido não encontrado');
    }

    // Encontrar o cliente dono do pedido
    const customer = customerStore.getCustomerById(orderToUpdate.userId || '');
    if (!customer) {
      throw new Error('Cliente não encontrado');
    }

    // Atualizar o código de rastreamento no pedido
    const orderResult = customerStore.getOrderById(orderToUpdate.id);
    if (!orderResult) {
      throw new Error('Pedido não encontrado no cliente');
    }

    // Atualizar o pedido no Firestore
    const customerRef = doc($db, 'users', customer.id);

    // Atualizar o pedido na lista local
    orderToUpdate.trackCode = trackCodeInput.value;

    // Atualizar o pedido selecionado
    if (selectedOrder.value) {
      selectedOrder.value.trackCode = trackCodeInput.value;
    }

    // Sair do modo de edição
    editingTrackCode.value = false;

    console.log(`✅ Código de rastreamento atualizado para o pedido ${selectedOrder.value?.id}`);
  } catch (e) {
    console.error('Erro ao atualizar código de rastreamento:', e);
    alert('Ocorreu um erro ao atualizar o código de rastreamento. Por favor, tente novamente.');
  } finally {
    loading.value = false;
  }
};

// Observar mudanças na rota para filtrar por cliente
watch(() => route.query.clientId, (newClientId) => {
  if (newClientId && typeof newClientId === 'string') {
    filterOrdersByClient(newClientId)
  } else {
    filteredOrders.value = [...orders.value]
  }
})

onMounted(async () => {
  try {
    await fetchOrders()

    // Verificar se há um ID de cliente na query para filtrar
    const clientId = route.query.clientId
    if (clientId && typeof clientId === 'string') {
      filterOrdersByClient(clientId)
    }
  } catch (error) {
    console.error('❌ Erro ao inicializar página:', error)
  }
})
</script>
