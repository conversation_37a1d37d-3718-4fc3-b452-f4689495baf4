<template>
    <!-- Header Admin -->
    <HeaderAdmin />

    <div class="min-h-screen bg-gray-100 py-8">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center mb-8">
                <h1 class="text-2xl font-bold">Gerenciar Pedidos dos Clientes</h1>

                <!-- Search bar -->
                <div class="flex items-center w-1/2">
                    <div class="relative w-full">
                        <input v-model="searchTerm" type="text" placeholder="Buscar pedidos por cliente, produto ou status..."
                            class="w-full px-4 py-2 pr-10 rounded-lg border focus:outline-none focus:ring-2 focus:ring-red-500"
                            @input="debouncedSearch" />
                        <i class="fas fa-search absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                <div class="flex flex-wrap gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select v-model="statusFilter" class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500">
                            <option value="">Todos</option>
                            <option value="approved">Aprovado</option>
                            <option value="pending">Pendente</option>
                            <option value="rejected">Rejeitado</option>
                            <option value="em produção">Em Produção</option>
                            <option value="em fila de produção">Em Fila de Produção</option>
                            <option value="enviado">Enviado</option>
                            <option value="concluída">Concluída</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Período</label>
                        <select v-model="periodFilter" class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500">
                            <option value="">Todos</option>
                            <option value="today">Hoje</option>
                            <option value="week">Últimos 7 dias</option>
                            <option value="month">Último mês</option>
                            <option value="year">Último ano</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Forma de Pagamento</label>
                        <select v-model="paymentFilter" class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500">
                            <option value="">Todas</option>
                            <option value="mercadopago">MercadoPago</option>
                            <option value="stripe">Stripe</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tipo de Compra</label>
                        <select v-model="testFilter" class="w-full px-3 py-2 border rounded-md focus:ring-red-500 focus:border-red-500">
                            <option value="all">Todas</option>
                            <option value="real">Compras Reais</option>
                            <option value="test">Compras de Teste</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button @click="applyFilters" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                            Aplicar Filtros
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading state -->
            <div v-if="loading" class="space-y-4">
                <OrderSkeleton v-for="i in 5" :key="i" />
            </div>

            <!-- Error state -->
            <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <p class="font-bold">Erro:</p>
                <p>{{ error }}</p>
                <button @click="() => loadOrders()" class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors">
                    Tentar Novamente
                </button>
            </div>

            <!-- Empty state -->
            <div v-else-if="!orders.length" class="text-center py-8 bg-white rounded-lg shadow-md">
                <i class="fas fa-shopping-cart text-gray-300 text-5xl mb-4"></i>
                <p class="text-gray-600" v-if="searchTerm || statusFilter || periodFilter || paymentFilter">
                    Nenhum pedido encontrado com os filtros aplicados.
                </p>
                <p class="text-gray-600" v-else>Nenhum pedido encontrado.</p>
            </div>

            <!-- Orders list -->
            <div v-else>
                <div class="bg-white rounded-lg shadow-md overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    ID do Pedido
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                                    Cliente
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Produto
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                    Valor
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                                    Data
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                                    Pagamento
                                </th>
                                <th scope="col" class="sticky right-0 bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-l border-gray-200">
                                    Ações
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="order in orders" :key="order.id" class="hover:bg-gray-50 group">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ order.paymentId }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">
                                    <div class="flex items-center">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ order.customerName || 'Cliente' }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ order.customerEmail }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 flex-shrink-0">
                                            <img v-if="order.productImage" :src="order.productImage" :alt="order.productName" class="h-10 w-10 rounded-full object-cover">
                                            <div v-else class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                <i class="fas fa-box text-gray-400"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ order.productName || 'Produto' }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                ID: {{ order.productId }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden sm:table-cell">
                                    {{ formatCurrency(order.total) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">
                                    {{ formatDate(order.createdAt) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex flex-col space-y-1">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                                            :class="getStatusClass(order.status)">
                                            {{ getStatusText(order.status) }}
                                        </span>
                                        <span v-if="order.isTest" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                            Teste
                                        </span>
                                        <span v-if="order.trackCode" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            <i class="fas fa-truck mr-1"></i> Rastreável
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden lg:table-cell">
                                    {{ getPaymentMethodText(order.paymentProvider, order.paymentType) }}
                                </td>
                                <td class="sticky right-0 bg-white px-6 py-4 whitespace-normal text-sm font-medium border-l border-gray-200">
                                    <div class="flex flex-wrap gap-2">
                                        <button @click="viewOrderDetails(order)" class="px-2 py-1 bg-blue-100 text-blue-600 hover:bg-blue-200 rounded">
                                            Detalhes
                                        </button>
                                        <button v-if="order.status === 'pending'" @click="updateOrderStatus(order.id, 'approved')" class="px-2 py-1 bg-green-100 text-green-600 hover:bg-green-200 rounded">
                                            Aprovar
                                        </button>
                                        <button v-if="order.status === 'pending'" @click="updateOrderStatus(order.id, 'rejected')" class="px-2 py-1 bg-red-100 text-red-600 hover:bg-red-200 rounded">
                                            Rejeitar
                                        </button>
                                        <button v-if="order.status === 'approved'" @click="updateOrderStatus(order.id, 'em fila de produção')" class="px-2 py-1 bg-purple-100 text-purple-600 hover:bg-purple-200 rounded">
                                            Fila
                                        </button>
                                        <button v-if="order.status === 'em fila de produção'" @click="updateOrderStatus(order.id, 'em produção')" class="px-2 py-1 bg-blue-100 text-blue-600 hover:bg-blue-200 rounded">
                                            Produção
                                        </button>
                                        <button v-if="order.status === 'em produção'" @click="updateOrderStatus(order.id, 'enviado')" class="px-2 py-1 bg-teal-100 text-teal-600 hover:bg-teal-200 rounded">
                                            Enviado
                                        </button>
                                        <button v-if="order.status === 'enviado'" @click="updateOrderStatus(order.id, 'concluída')" class="px-2 py-1 bg-emerald-100 text-emerald-600 hover:bg-emerald-200 rounded">
                                            Concluída
                                        </button>
                                        <button v-if="order.trackCode" @click="trackOrder(order)" class="px-2 py-1 bg-blue-100 text-blue-600 hover:bg-blue-200 rounded">
                                            <i class="fas fa-truck mr-1"></i> Rastrear
                                        </button>
                                        <button @click="confirmDeleteOrder(order)" class="px-2 py-1 bg-red-100 text-red-600 hover:bg-red-200 rounded">
                                            <i class="fas fa-trash mr-1"></i> Excluir
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Paginação -->
                <div class="flex items-center justify-between mt-6">
                    <div class="text-sm text-gray-700">
                        Mostrando <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> a
                        <span class="font-medium">{{ Math.min(currentPage * pageSize, totalItems) }}</span> de
                        <span class="font-medium">{{ totalItems }}</span> resultados
                    </div>
                    <div class="flex items-center space-x-2">
                        <button @click="changePage(currentPage - 1)" :disabled="currentPage === 1"
                            class="px-3 py-1 rounded border"
                            :class="currentPage === 1 ? 'text-gray-400 border-gray-200' : 'text-gray-600 border-gray-300 hover:bg-gray-50'">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="flex items-center space-x-1">
                            <template v-for="page in totalPages" :key="page">
                                <button v-if="page === currentPage"
                                    class="px-3 py-1 rounded bg-red-600 text-white">
                                    {{ page }}
                                </button>
                                <button v-else-if="shouldShowPageButton(page)" @click="changePage(page)"
                                    class="px-3 py-1 rounded border border-gray-300 hover:bg-gray-50">
                                    {{ page }}
                                </button>
                                <span v-else-if="page === currentPage - 2 || page === currentPage + 2"
                                    class="px-2">...</span>
                            </template>
                        </div>
                        <button @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages"
                            class="px-3 py-1 rounded border"
                            :class="currentPage === totalPages ? 'text-gray-400 border-gray-200' : 'text-gray-600 border-gray-300 hover:bg-gray-50'">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Detalhes do Pedido -->
    <div v-if="showOrderDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div class="bg-white rounded-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div class="sticky top-0 bg-white p-6 border-b flex justify-between items-center z-10">
                <h2 class="text-xl font-bold">Detalhes do Pedido</h2>
                <button @click="showOrderDetailsModal = false" class="text-gray-500 hover:text-gray-800">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6" v-if="selectedOrder">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <h3 class="text-lg font-semibold mb-3">Informações do Pedido</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="grid grid-cols-2 gap-2">
                                <div class="text-sm text-gray-600">ID do Pedido:</div>
                                <div class="text-sm font-medium">{{ selectedOrder.id }}</div>

                                <div class="text-sm text-gray-600">Status:</div>
                                <div class="text-sm font-medium">
                                    <div class="flex flex-col space-y-1">
                                        <div class="flex items-center space-x-2">
                                            <span class="px-2 py-1 text-xs rounded-full" :class="getStatusClass(selectedOrder.status)">
                                                {{ getStatusText(selectedOrder.status) }}
                                            </span>
                                            <button
                                                @click="startEditStatus"
                                                class="text-blue-600 hover:text-blue-800 text-xs"
                                                title="Editar status"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                        <span v-if="selectedOrder.isTest" class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">
                                            Compra de Teste
                                        </span>
                                    </div>
                                </div>

                                <div class="text-sm text-gray-600">Data:</div>
                                <div class="text-sm font-medium">{{ formatDate(selectedOrder.createdAt) }}</div>

                                <div class="text-sm text-gray-600">Valor Total:</div>
                                <div class="text-sm font-medium">{{ formatCurrency(selectedOrder.total) }}</div>

                                <div class="text-sm text-gray-600">Forma de Pagamento:</div>
                                <div class="text-sm font-medium">{{ getPaymentMethodText(selectedOrder.paymentProvider, selectedOrder.paymentType) }}</div>

                                <div class="text-sm text-gray-600">ID do Pagamento:</div>
                                <div class="text-sm font-medium">{{ selectedOrder.paymentId }}</div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold mb-3">Informações do Cliente</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="grid grid-cols-2 gap-2">
                                <div class="text-sm text-gray-600">Nome:</div>
                                <div class="text-sm font-medium">{{ selectedOrder.customerName || 'N/A' }}</div>

                                <div class="text-sm text-gray-600">Email:</div>
                                <div class="text-sm font-medium">{{ selectedOrder.customerEmail }}</div>

                                <div class="text-sm text-gray-600">Telefone:</div>
                                <div class="text-sm font-medium">{{ selectedOrder.customerPhone || 'N/A' }}</div>

                                <div class="text-sm text-gray-600">ID do Cliente:</div>
                                <div class="text-sm font-medium">{{ selectedOrder.customerId }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-3">Produto</h3>
                    <div class="bg-gray-50 p-4 rounded-lg flex items-center">
                        <div class="h-16 w-16 flex-shrink-0">
                            <img v-if="selectedOrder.productImage" :src="selectedOrder.productImage" :alt="selectedOrder.productName" class="h-16 w-16 object-cover rounded">
                            <div v-else class="h-16 w-16 rounded bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-box text-gray-400 text-2xl"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-lg font-medium">{{ selectedOrder.productName || 'Produto' }}</div>
                            <div class="text-sm text-gray-600">ID: {{ selectedOrder.productId }}</div>
                            <div class="text-sm text-gray-600">Quantidade: {{ selectedOrder.quantity || 1 }}</div>
                            <div class="text-sm font-medium text-red-600">{{ formatCurrency(selectedOrder.total) }}</div>
                        </div>
                    </div>
                </div>

                <div v-if="selectedOrder.shipping" class="mb-6">
                    <h3 class="text-lg font-semibold mb-3">Endereço de Entrega</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="text-sm">
                            <p class="font-medium">{{ selectedOrder.shipping.name || 'Endereço de Entrega' }}</p>
                            <p>{{ selectedOrder.shipping.street }}, {{ selectedOrder.shipping.number }}</p>
                            <p v-if="selectedOrder.shipping.complement">{{ selectedOrder.shipping.complement }}</p>
                            <p>{{ selectedOrder.shipping.neighborhood }}, {{ selectedOrder.shipping.city }} - {{ selectedOrder.shipping.state }}</p>
                            <p>CEP: {{ selectedOrder.shipping.zipCode }}</p>
                        </div>
                    </div>
                </div>

                <!-- Código de Rastreamento -->
                <div class="mb-6 mt-6">
                    <h3 class="text-lg font-semibold mb-3">Código de Rastreamento</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div v-if="!editingTrackCode" class="flex flex-wrap justify-between items-center gap-2">
                            <div class="flex-grow">
                                <p v-if="selectedOrder.trackCode" class="text-sm font-medium break-all">{{ selectedOrder.trackCode }}</p>
                                <p v-else class="text-sm text-gray-500 italic">Nenhum código de rastreamento informado</p>
                            </div>
                            <div class="flex flex-wrap gap-2">
                                <button v-if="selectedOrder.trackCode" @click="trackOrder(selectedOrder)"
                                    class="px-3 py-1 bg-green-100 text-green-600 hover:bg-green-200 rounded text-sm font-medium flex-shrink-0">
                                    <i class="fas fa-truck mr-1"></i> Rastrear
                                </button>
                                <button @click="startEditTrackCode"
                                    class="px-3 py-1 bg-blue-100 text-blue-600 hover:bg-blue-200 rounded text-sm font-medium flex-shrink-0">
                                    {{ selectedOrder.trackCode ? 'Editar' : 'Adicionar' }}
                                </button>
                            </div>
                        </div>
                        <div v-else class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Código de Rastreamento</label>
                                <input v-model="trackCodeInput" type="text" placeholder="Digite o código de rastreamento"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent" />
                            </div>
                            <div class="flex flex-wrap justify-end gap-2">
                                <button @click="cancelEditTrackCode"
                                    class="px-3 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                    Cancelar
                                </button>
                                <button @click="saveTrackCode"
                                    class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    Salvar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="border-t pt-6 flex flex-wrap justify-between gap-4">
                    <div>
                        <button @click="toggleTestFlag(selectedOrder)"
                            class="px-4 py-2 border rounded-md hover:bg-gray-50 mb-2 sm:mb-0 w-full sm:w-auto"
                            :class="selectedOrder.isTest ? 'border-purple-500 text-purple-700' : 'border-gray-300 text-gray-700'">
                            <i class="fas fa-flask mr-2"></i>
                            {{ selectedOrder.isTest ? 'Remover Flag de Teste' : 'Marcar como Teste' }}
                        </button>
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <button v-if="selectedOrder.status === 'pending'" @click="updateOrderStatus(selectedOrder.id, 'approved')"
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 w-full sm:w-auto">
                            Aprovar Pedido
                        </button>
                        <button v-if="selectedOrder.status === 'pending'" @click="updateOrderStatus(selectedOrder.id, 'rejected')"
                            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 w-full sm:w-auto">
                            Rejeitar Pedido
                        </button>
                        <button v-if="selectedOrder.status === 'approved'" @click="updateOrderStatus(selectedOrder.id, 'em fila de produção')"
                            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 w-full sm:w-auto">
                            Em Fila de Produção
                        </button>
                        <button v-if="selectedOrder.status === 'em fila de produção'" @click="updateOrderStatus(selectedOrder.id, 'em produção')"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 w-full sm:w-auto">
                            Em Produção
                        </button>
                        <button v-if="selectedOrder.status === 'em produção'" @click="updateOrderStatus(selectedOrder.id, 'enviado')"
                            class="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 w-full sm:w-auto">
                            Enviado
                        </button>
                        <button v-if="selectedOrder.status === 'enviado'" @click="updateOrderStatus(selectedOrder.id, 'concluída')"
                            class="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 w-full sm:w-auto">
                            Concluída
                        </button>
                        <button @click="confirmDeleteOrder(selectedOrder)"
                            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 w-full sm:w-auto">
                            <i class="fas fa-trash mr-2"></i> Excluir Pedido
                        </button>
                        <button @click="showOrderDetailsModal = false"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 w-full sm:w-auto">
                            Fechar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Edição de Status -->
    <Teleport to="body">
      <div v-if="showStatusModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4" style="z-index: 9999;">
        <div class="bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div class="sticky top-0 bg-white p-6 border-b flex justify-between items-center z-10">
                <h2 class="text-xl font-bold">Editar Status do Pedido</h2>
                <button @click="closeStatusModal" class="text-gray-500 hover:text-gray-800">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status atual:</label>
                    <span
                        class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                        :class="getStatusClass(selectedOrder?.status)"
                    >
                        {{ getStatusText(selectedOrder?.status) }}
                    </span>
                </div>

                <div class="mb-4">
                    <label for="newStatus" class="block text-sm font-medium text-gray-700 mb-2">Novo status:</label>
                    <select
                        id="newStatus"
                        v-model="statusInput"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
                    >
                        <option value="approved">Aprovado</option>
                        <option value="pending">Pendente</option>
                        <option value="rejected">Rejeitado</option>
                        <option value="em produção">Em Produção</option>
                        <option value="em fila de produção">Em Fila de Produção</option>
                        <option value="enviado">Enviado</option>
                        <option value="concluída">Concluída</option>
                    </select>
                </div>

                <div class="flex justify-end space-x-2">
                    <button
                        @click="closeStatusModal"
                        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                        Cancelar
                    </button>
                    <button
                        @click="saveOrderStatus"
                        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                        :disabled="statusSaving"
                    >
                        <span v-if="statusSaving" class="mr-2">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                        Salvar
                    </button>
                </div>
            </div>
        </div>
      </div>
    </Teleport>

    <!-- Modal de Confirmação de Exclusão -->
    <Teleport to="body">
      <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4" style="z-index: 9999;">
        <div class="bg-white rounded-lg w-full max-w-md">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-gray-900">Confirmar Exclusão</h3>
                    </div>
                </div>

                <div class="mb-4">
                    <p class="text-sm text-gray-600">
                        Tem certeza que deseja excluir este pedido? Esta ação não pode ser desfeita.
                    </p>
                    <div v-if="orderToDelete" class="mt-3 p-3 bg-gray-50 rounded-lg">
                        <p class="text-sm"><strong>ID do Pedido:</strong> {{ orderToDelete.paymentId }}</p>
                        <p class="text-sm"><strong>Cliente:</strong> {{ orderToDelete.customerName }}</p>
                        <p class="text-sm"><strong>Produto:</strong> {{ orderToDelete.productName }}</p>
                        <p class="text-sm"><strong>Status:</strong> {{ getStatusText(orderToDelete.status) }}</p>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button
                        @click="cancelDeleteOrder"
                        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        :disabled="deleting"
                    >
                        Cancelar
                    </button>
                    <button
                        @click="deleteOrder"
                        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                        :disabled="deleting"
                    >
                        <span v-if="deleting" class="mr-2">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                        {{ deleting ? 'Excluindo...' : 'Excluir' }}
                    </button>
                </div>
            </div>
        </div>
      </div>
    </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import HeaderAdmin from '~/components/HeaderAdmin.vue';
import OrderSkeleton from '~/components/admin/OrderSkeleton.vue';
import { useCustomerStore } from '~/stores/customerStore';
import { useProductsStore } from '~/stores/productsStore';
import type { IOrder, IPendingOrder, IRejectedOrder } from '~/types/customer';
import type { Figure } from '~/types/figures';
import _ from 'lodash';

// Definição de tipos
interface Order {
    id: string;
    customerId: string;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    productId: string;
    productName?: string;
    productImage?: string;
    total: number;
    quantity: number;
    status: string;
    paymentProvider: string;
    paymentType: string;
    paymentId: string;
    createdAt: Date;
    isTest?: boolean; // Flag para indicar se é uma compra de teste
    trackCode?: string; // Código de rastreamento dos Correios
    shipping?: {
        name: string;
        street: string;
        number: string;
        complement?: string;
        neighborhood: string;
        city: string;
        state: string;
        zipCode: string;
    };
}

// Middleware para proteger a rota
definePageMeta({
  middleware: ['admin']
})

// Estado
const orders = ref<Order[]>([]);
const loading = ref(true);
const error = ref('');
const searchTerm = ref('');
const statusFilter = ref('');
const periodFilter = ref('');
const paymentFilter = ref('');
const testFilter = ref('all'); // 'all', 'test', 'real'
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const showOrderDetailsModal = ref(false);
const selectedOrder = ref<Order | null>(null);
const editingTrackCode = ref(false);
const trackCodeInput = ref('');
const showTrackingModal = ref(false);
const trackingInfo = ref<any>(null);
const trackingLoading = ref(false);
const trackingError = ref('');

// Estado para o modal de edição de status
const showStatusModal = ref(false);
const statusInput = ref('');
const statusSaving = ref(false);

// Estado para o modal de confirmação de exclusão
const showDeleteModal = ref(false);
const orderToDelete = ref<Order | null>(null);
const deleting = ref(false);

// Computed
const totalPages = computed(() => {
    return Math.ceil(totalItems.value / pageSize.value);
});

// Métodos
const loadOrders = async (forceRefresh = false) => {
    loading.value = true;
    error.value = '';

    try {
        // Se forceRefresh for true, invalidar o cache primeiro
        if (forceRefresh) {
            console.log('🔄 [Frontend] Forçando refresh do cache...');
            await $fetch('/api/customers?refresh=true');
        }

        // Buscar todos os clientes através da store
        const customerStore = useCustomerStore();
        await customerStore.fetchAllCustomers();

        // Buscar produtos para obter informações detalhadas
        const productsStore = useProductsStore();
        if (!productsStore.initialized) {
            await productsStore.fetchAllProducts();
        }

        // Processar todos os pedidos de todos os clientes
        const allOrders: Order[] = [];

        customerStore.customers.forEach(customer => {
            // Processar pedidos aprovados
            if (customer.orders && customer.orders.length > 0) {
                customer.orders.forEach(order => {
                    // Encontrar o produto associado ao pedido
                    const product = productsStore.getProductById(order.productId);

                    // Aplicar filtros
                    if (shouldIncludeOrder(order, customer, product)) {
                        allOrders.push({
                            id: order.id,
                            customerId: customer.id || '',
                            customerName: customer.name,
                            customerEmail: customer.email,
                            customerPhone: customer.phone,
                            productId: order.productId,
                            productName: product?.title || 'Produto não encontrado',
                            productImage: product?.gallery_imgs?.[0] || '',
                            total: order.total || 0,
                            quantity: order.quantity || 1,
                            status: order.status || order.collectionStatus || 'approved',
                            paymentProvider: order.paymentProvider || 'mercadopago',
                            paymentType: order.paymentType || '',
                            paymentId: order.paymentId || '',
                            createdAt: order.createdAt,
                            isTest: order.isTest || false,
                            trackCode: order.trackCode || '',
                            shipping: order.shipping
                        });
                    }
                });
            }

            // Processar pedidos pendentes
            if (customer.pendingOrders && customer.pendingOrders.length > 0) {
                customer.pendingOrders.forEach(order => {
                    // Encontrar o produto associado ao pedido
                    const product = productsStore.getProductById(order.productId || '');

                    // Aplicar filtros
                    if (shouldIncludeOrder(order, customer, product)) {
                        allOrders.push({
                            id: order.id,
                            customerId: customer.id || '',
                            customerName: customer.name,
                            customerEmail: customer.email,
                            customerPhone: customer.phone,
                            productId: order.productId || '',
                            productName: product?.title || 'Produto não encontrado',
                            productImage: product?.gallery_imgs?.[0] || '',
                            total: order.total || 0,
                            quantity: order.quantity || 1,
                            status: 'pending',
                            paymentProvider: order.paymentProvider || 'mercadopago',
                            paymentType: order.paymentType || '',
                            paymentId: order.paymentId || '',
                            createdAt: order.createdAt,
                            isTest: order.isTest || false,
                            trackCode: order.trackCode || '',
                            shipping: order.shipping
                        });
                    }
                });
            }

            // Processar pedidos rejeitados
            if (customer.rejectedOrders && customer.rejectedOrders.length > 0) {
                customer.rejectedOrders.forEach(order => {
                    // Encontrar o produto associado ao pedido
                    const product = productsStore.getProductById(order.productId || '');

                    // Aplicar filtros
                    if (shouldIncludeOrder(order, customer, product)) {
                        allOrders.push({
                            id: order.id,
                            customerId: customer.id || '',
                            customerName: customer.name,
                            customerEmail: customer.email,
                            customerPhone: customer.phone,
                            productId: order.productId || '',
                            productName: product?.title || 'Produto não encontrado',
                            productImage: product?.gallery_imgs?.[0] || '',
                            total: order.total || 0,
                            quantity: order.quantity || 1,
                            status: 'rejected',
                            paymentProvider: order.paymentProvider || 'mercadopago',
                            paymentType: order.paymentType || '',
                            paymentId: order.paymentId || '',
                            createdAt: order.createdAt,
                            isTest: order.isTest || false,
                            trackCode: order.trackCode || '',
                            shipping: order.shipping
                        });
                    }
                });
            }
        });

        // Ordenar pedidos por data (mais recentes primeiro)
        allOrders.sort((a, b) => {
            const dateA = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt);
            const dateB = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt);
            return dateB.getTime() - dateA.getTime();
        });

        orders.value = allOrders;
        totalItems.value = allOrders.length;

        console.log(`✅ Carregados ${allOrders.length} pedidos de clientes`);
    } catch (e) {
        console.error('Erro ao carregar pedidos:', e);
        error.value = 'Ocorreu um erro ao carregar os pedidos. Por favor, tente novamente.';
    } finally {
        loading.value = false;
    }
};

// Função para verificar se um pedido deve ser incluído com base nos filtros
const shouldIncludeOrder = (order: IOrder | IPendingOrder | IRejectedOrder, customer: any, product: Figure | undefined) => {
    // Filtro de busca
    if (searchTerm.value) {
        const searchLower = searchTerm.value.toLowerCase();
        const customerNameMatch = customer.name?.toLowerCase().includes(searchLower);
        const customerEmailMatch = customer.email?.toLowerCase().includes(searchLower);
        const productNameMatch = product?.title?.toLowerCase().includes(searchLower);
        const orderIdMatch = order.id?.toLowerCase().includes(searchLower);
        const statusMatch = (order.status || order.collectionStatus || '').toLowerCase().includes(searchLower);

        if (!customerNameMatch && !customerEmailMatch && !productNameMatch && !orderIdMatch && !statusMatch) {
            return false;
        }
    }

    // Filtro de status
    if (statusFilter.value) {
        const orderStatus = order.status || order.collectionStatus || '';
        if (orderStatus !== statusFilter.value) {
            return false;
        }
    }

    // Filtro de período
    if (periodFilter.value) {
        const orderDate = order.createdAt instanceof Date ? order.createdAt : new Date(order.createdAt);
        const now = new Date();

        switch (periodFilter.value) {
            case 'today':
                // Verificar se é hoje
                if (orderDate.getDate() !== now.getDate() ||
                    orderDate.getMonth() !== now.getMonth() ||
                    orderDate.getFullYear() !== now.getFullYear()) {
                    return false;
                }
                break;
            case 'week':
                // Últimos 7 dias
                const weekAgo = new Date();
                weekAgo.setDate(now.getDate() - 7);
                if (orderDate < weekAgo) {
                    return false;
                }
                break;
            case 'month':
                // Último mês
                const monthAgo = new Date();
                monthAgo.setMonth(now.getMonth() - 1);
                if (orderDate < monthAgo) {
                    return false;
                }
                break;
            case 'year':
                // Último ano
                const yearAgo = new Date();
                yearAgo.setFullYear(now.getFullYear() - 1);
                if (orderDate < yearAgo) {
                    return false;
                }
                break;
        }
    }

    // Filtro de forma de pagamento
    if (paymentFilter.value && order.paymentProvider !== paymentFilter.value) {
        return false;
    }

    // Filtro de compras de teste
    if (testFilter.value !== 'all') {
        const isTestOrder = order.isTest === true;
        if (testFilter.value === 'test' && !isTestOrder) {
            return false;
        }
        if (testFilter.value === 'real' && isTestOrder) {
            return false;
        }
    }

    return true;
};

const debouncedSearch = _.debounce(() => {
    currentPage.value = 1;
    loadOrders();
}, 500);

const applyFilters = () => {
    currentPage.value = 1;
    loadOrders();
};

const changePage = (page: number) => {
    if (page < 1 || page > totalPages.value) return;
    currentPage.value = page;
    loadOrders();
};

const shouldShowPageButton = (page: number) => {
    const current = currentPage.value;

    // Sempre mostrar primeira e última página
    if (page === 1 || page === totalPages.value) return true;

    // Mostrar páginas ao redor da página atual
    return Math.abs(page - current) <= 1;
};

const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(value);
};

const formatDate = (date: any) => {
    if (!date) return 'Data não disponível';

    // Log para depuração - ver o formato exato do timestamp recebido
    console.log('Tipo de timestamp recebido em formatDate:', typeof date, date);

    try {
        let dateObj;

        // Verificar o tipo de timestamp
        if (date instanceof Date) {
            dateObj = date;
            console.log('Timestamp é um objeto Date');
        } else if (typeof date === 'object' && date.seconds !== undefined) {
            // Formato do Firestore timestamp
            const totalMilliseconds = date.seconds * 1000 + (date.nanoseconds || 0) / 1e6;
            dateObj = new Date(totalMilliseconds);
            console.log('Timestamp é um objeto Firestore, convertido para:', dateObj);
        } else if (typeof date === 'string') {
            // String de data
            dateObj = new Date(date);
            console.log('Timestamp é uma string, convertido para:', dateObj);
        } else if (typeof date === 'number') {
            // Timestamp em milissegundos
            dateObj = new Date(date);
            console.log('Timestamp é um número, convertido para:', dateObj);
        } else if (date && typeof date.toDate === 'function') {
            // Objeto Firestore com método toDate()
            dateObj = date.toDate();
            console.log('Timestamp tem método toDate(), convertido para:', dateObj);
        } else {
            // Tentar converter qualquer outro formato
            dateObj = new Date(date);
            console.log('Timestamp é de outro formato, tentativa de conversão para:', dateObj);
        }

        // Verificar se a data é válida
        if (isNaN(dateObj.getTime())) {
            console.warn('Data inválida:', date);
            return 'Data inválida';
        }

        // Formatar a data e hora sem os segundos
        const options: Intl.DateTimeFormatOptions = {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };

        // Tentar formatar sem especificar timezone primeiro
        const formattedDate = dateObj.toLocaleDateString('pt-BR', options)
            .replace(',', '') + ' ' + dateObj.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
            });

        console.log('Data formatada:', formattedDate);
        return formattedDate;
    } catch (error) {
        console.error('Erro ao formatar data:', error, date);
        return 'Data inválida';
    }
};

const getStatusClass = (status: string) => {
    switch (status) {
        case 'approved':
            return 'bg-green-100 text-green-800';
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'rejected':
            return 'bg-red-100 text-red-800';
        case 'em produção':
            return 'bg-blue-100 text-blue-800';
        case 'em fila de produção':
            return 'bg-purple-100 text-purple-800';
        case 'enviado':
            return 'bg-teal-100 text-teal-800';
        case 'shipped':
            return 'bg-teal-100 text-teal-800';
        case 'concluída':
            return 'bg-emerald-100 text-emerald-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getStatusText = (status: string) => {
    switch (status) {
        case 'approved':
            return 'Aprovado';
        case 'pending':
            return 'Pendente';
        case 'rejected':
            return 'Rejeitado';
        case 'em produção':
            return 'Em Produção';
        case 'em fila de produção':
            return 'Em Fila de Produção';
        case 'enviado':
            return 'Enviado';
        case 'shipped':
            return 'Enviado';
        case 'concluída':
            return 'Concluída';
        default:
            return status;
    }
};

const getPaymentMethodText = (provider: string, type: string) => {
    let methodText = '';

    switch (provider) {
        case 'mercadopago':
            methodText = 'MercadoPago';
            break;
        case 'stripe':
            methodText = 'Stripe';
            break;
        default:
            methodText = provider;
    }

    switch (type) {
        case 'credit_card':
            return `${methodText} - Cartão de Crédito`;
        case 'debit_card':
            return `${methodText} - Cartão de Débito`;
        case 'pix':
            return `${methodText} - PIX`;
        case 'card':
            return `${methodText} - Cartão`;
        default:
            return methodText;
    }
};

const viewOrderDetails = (order: Order) => {
    selectedOrder.value = order;
    showOrderDetailsModal.value = true;
    // Resetar o estado de edição do código de rastreamento
    editingTrackCode.value = false;
    trackCodeInput.value = order.trackCode || '';
};

// Função para rastrear o pedido usando a API dos Correios
const trackOrder = async (order: Order) => {
    if (!order.trackCode) {
        error.value = 'Este pedido não possui código de rastreamento.';
        return;
    }

    trackingLoading.value = true;
    trackingError.value = '';
    trackingInfo.value = null;
    showTrackingModal.value = true;

    try {
        // Usar a API pública dos Correios para rastreamento
        // Exemplo: https://proxyapp.correios.com.br/v1/sro-rastro/{CODIGO}
        const trackCode = order.trackCode.trim();

        // Abrir o site dos Correios em uma nova aba com o código de rastreamento
        window.open(`https://rastreamento.correios.com.br/app/index.php?objetos=${trackCode}`, '_blank');

        // Alternativa: implementar uma chamada à API dos Correios no futuro
        // const response = await fetch(`/api/track?code=${trackCode}`);
        // const data = await response.json();
        //
        // if (data.error) {
        //     throw new Error(data.error);
        // }
        //
        // trackingInfo.value = data;
    } catch (e: any) {
        console.error('Erro ao rastrear pedido:', e);
        trackingError.value = e.message || 'Ocorreu um erro ao rastrear o pedido. Por favor, tente novamente.';
    } finally {
        trackingLoading.value = false;
    }
};

// Funções para gerenciar o código de rastreamento
const startEditTrackCode = () => {
    if (!selectedOrder.value) return;
    trackCodeInput.value = selectedOrder.value.trackCode || '';
    editingTrackCode.value = true;
};

const cancelEditTrackCode = () => {
    editingTrackCode.value = false;
};

const saveTrackCode = async () => {
    if (!selectedOrder.value) return;

    loading.value = true;

    try {
        // Encontrar o pedido na lista
        const orderToUpdate = orders.value.find(o => o.id === selectedOrder.value?.id);
        if (!orderToUpdate) {
            throw new Error('Pedido não encontrado');
        }

        // Encontrar o cliente dono do pedido
        const customerStore = useCustomerStore();

        // Verificar se o cliente já está carregado, caso contrário, buscá-lo
        let customer = customerStore.getCustomerById(orderToUpdate.customerId);
        if (!customer) {
            // Tentar buscar o cliente diretamente
            customer = await customerStore.fetchCustomerById(orderToUpdate.customerId);
            if (!customer) {
                throw new Error('Cliente não encontrado');
            }
        }

        // Atualizar o código de rastreamento no pedido com base no status
        let orderUpdated = false;

        if (orderToUpdate.status === 'approved') {
            // Atualizar pedido aprovado
            const orderIndex = customer.orders.findIndex(o => o.id === selectedOrder.value?.id);
            if (orderIndex !== -1) {
                // Verificar se o campo trackCode já existe, se não, adicioná-lo
                if (!customer.orders[orderIndex].hasOwnProperty('trackCode')) {
                    customer.orders[orderIndex] = {
                        ...customer.orders[orderIndex],
                        trackCode: trackCodeInput.value
                    };
                } else {
                    customer.orders[orderIndex].trackCode = trackCodeInput.value;
                }
                orderUpdated = true;
            }
        } else if (orderToUpdate.status === 'pending') {
            // Atualizar pedido pendente
            const orderIndex = customer.pendingOrders.findIndex(o => o.id === selectedOrder.value?.id);
            if (orderIndex !== -1) {
                // Verificar se o campo trackCode já existe, se não, adicioná-lo
                if (!customer.pendingOrders[orderIndex].hasOwnProperty('trackCode')) {
                    customer.pendingOrders[orderIndex] = {
                        ...customer.pendingOrders[orderIndex],
                        trackCode: trackCodeInput.value
                    };
                } else {
                    customer.pendingOrders[orderIndex].trackCode = trackCodeInput.value;
                }
                orderUpdated = true;
            }
        } else if (orderToUpdate.status === 'rejected') {
            // Atualizar pedido rejeitado
            const orderIndex = customer.rejectedOrders.findIndex(o => o.id === selectedOrder.value?.id);
            if (orderIndex !== -1) {
                // Verificar se o campo trackCode já existe, se não, adicioná-lo
                if (!customer.rejectedOrders[orderIndex].hasOwnProperty('trackCode')) {
                    customer.rejectedOrders[orderIndex] = {
                        ...customer.rejectedOrders[orderIndex],
                        trackCode: trackCodeInput.value
                    };
                } else {
                    customer.rejectedOrders[orderIndex].trackCode = trackCodeInput.value;
                }
                orderUpdated = true;
            }
        }

        if (!orderUpdated) {
            throw new Error('Não foi possível atualizar o código de rastreamento');
        }

        // Atualizar o cliente no Firebase
        await customerStore.updateCustomer(customer);

        // Atualizar o pedido na lista local
        const orderIndex = orders.value.findIndex(o => o.id === selectedOrder.value?.id);
        if (orderIndex !== -1) {
            orders.value[orderIndex].trackCode = trackCodeInput.value;
        }

        // Atualizar o pedido selecionado
        if (selectedOrder.value) {
            selectedOrder.value.trackCode = trackCodeInput.value;
        }

        // Sair do modo de edição
        editingTrackCode.value = false;

        console.log(`✅ Código de rastreamento atualizado para o pedido ${selectedOrder.value?.id}`);
    } catch (e) {
        console.error('Erro ao atualizar código de rastreamento:', e);
        error.value = 'Ocorreu um erro ao atualizar o código de rastreamento. Por favor, tente novamente.';
    } finally {
        loading.value = false;
    }
};

const toggleTestFlag = async (order: Order) => {
    loading.value = true;

    try {
        // Encontrar o pedido na lista
        const orderToUpdate = orders.value.find(o => o.id === order.id);
        if (!orderToUpdate) {
            throw new Error('Pedido não encontrado');
        }

        // Encontrar o cliente dono do pedido
        const customerStore = useCustomerStore();

        // Verificar se o cliente já está carregado, caso contrário, buscá-lo
        let customer = customerStore.getCustomerById(orderToUpdate.customerId);
        if (!customer) {
            // Tentar buscar o cliente diretamente
            customer = await customerStore.fetchCustomerById(orderToUpdate.customerId);
            if (!customer) {
                throw new Error('Cliente não encontrado');
            }
        }

        // Inverter o valor da flag isTest
        const newTestValue = !order.isTest;

        // Atualizar o pedido no cliente com base no status
        let orderUpdated = false;

        if (order.status === 'approved') {
            // Atualizar pedido aprovado
            const orderIndex = customer.orders.findIndex(o => o.id === order.id);
            if (orderIndex !== -1) {
                customer.orders[orderIndex].isTest = newTestValue;
                orderUpdated = true;
            }
        } else if (order.status === 'pending') {
            // Atualizar pedido pendente
            const orderIndex = customer.pendingOrders.findIndex(o => o.id === order.id);
            if (orderIndex !== -1) {
                customer.pendingOrders[orderIndex].isTest = newTestValue;
                orderUpdated = true;
            }
        } else if (order.status === 'rejected') {
            // Atualizar pedido rejeitado
            const orderIndex = customer.rejectedOrders.findIndex(o => o.id === order.id);
            if (orderIndex !== -1) {
                customer.rejectedOrders[orderIndex].isTest = newTestValue;
                orderUpdated = true;
            }
        }

        if (!orderUpdated) {
            throw new Error('Não foi possível atualizar o pedido');
        }

        // Atualizar o cliente no Firebase
        await customerStore.updateCustomer(customer);

        // Atualizar o pedido na lista local
        const orderIndex = orders.value.findIndex(o => o.id === order.id);
        if (orderIndex !== -1) {
            orders.value[orderIndex].isTest = newTestValue;
        }

        // Atualizar o pedido selecionado
        if (selectedOrder.value && selectedOrder.value.id === order.id) {
            selectedOrder.value.isTest = newTestValue;
        }

        console.log(`✅ Pedido ${order.id} ${newTestValue ? 'marcado como teste' : 'desmarcado como teste'}`);
    } catch (e) {
        console.error('Erro ao atualizar flag de teste do pedido:', e);
        error.value = 'Ocorreu um erro ao atualizar o pedido. Por favor, tente novamente.';
    } finally {
        loading.value = false;
    }
};

// Funções para gerenciar o modal de edição de status
const startEditStatus = () => {
    if (!selectedOrder.value) return;
    statusInput.value = selectedOrder.value.status;
    showStatusModal.value = true;
};

const closeStatusModal = () => {
    showStatusModal.value = false;
    statusInput.value = '';
};

const saveOrderStatus = async () => {
    if (!selectedOrder.value) return;

    statusSaving.value = true;

    try {
        await updateOrderStatus(selectedOrder.value.id, statusInput.value);

        // Atualizar o status do pedido selecionado
        if (selectedOrder.value) {
            selectedOrder.value.status = statusInput.value;
        }

        // Fechar o modal
        closeStatusModal();
    } catch (error) {
        console.error('Erro ao salvar status do pedido:', error);
        alert('Ocorreu um erro ao salvar o status do pedido. Por favor, tente novamente.');
    } finally {
        statusSaving.value = false;
    }
};

const updateOrderStatus = async (orderId: string, newStatus: string) => {
    loading.value = true;

    try {
        // Encontrar o pedido na lista
        const orderToUpdate = orders.value.find(o => o.id === orderId);
        if (!orderToUpdate) {
            throw new Error('Pedido não encontrado');
        }

        // Encontrar o cliente dono do pedido
        const customerStore = useCustomerStore();

        // Verificar se o cliente já está carregado, caso contrário, buscá-lo
        let customer = customerStore.getCustomerById(orderToUpdate.customerId);
        if (!customer) {
            // Tentar buscar o cliente diretamente
            customer = await customerStore.fetchCustomerById(orderToUpdate.customerId);
            if (!customer) {
                throw new Error('Cliente não encontrado');
            }
        }

        // Atualizar o status do pedido no Firebase
        // Primeiro, precisamos encontrar o pedido original no cliente
        let orderUpdated = false;

        // Para os status "em produção", "em fila de produção", "enviado" e "concluída", apenas atualizamos o status sem mover o pedido
        if (newStatus === 'em produção' || newStatus === 'em fila de produção' || newStatus === 'enviado' || newStatus === 'shipped' || newStatus === 'concluída') {
            // Verificar em qual array o pedido está
            if (orderToUpdate.status === 'approved') {
                // Atualizar pedido aprovado
                const orderIndex = customer.orders.findIndex(o => o.id === orderId);
                if (orderIndex !== -1) {
                    customer.orders[orderIndex].status = newStatus;
                    customer.orders[orderIndex].updatedAt = new Date();
                    orderUpdated = true;
                }
            } else if (orderToUpdate.status === 'pending') {
                // Atualizar pedido pendente
                const orderIndex = customer.pendingOrders.findIndex(o => o.id === orderId);
                if (orderIndex !== -1) {
                    customer.pendingOrders[orderIndex].status = newStatus;
                    customer.pendingOrders[orderIndex].updatedAt = new Date();
                    orderUpdated = true;
                }
            } else if (orderToUpdate.status === 'rejected') {
                // Atualizar pedido rejeitado
                const orderIndex = customer.rejectedOrders.findIndex(o => o.id === orderId);
                if (orderIndex !== -1) {
                    customer.rejectedOrders[orderIndex].status = newStatus;
                    customer.rejectedOrders[orderIndex].updatedAt = new Date();
                    orderUpdated = true;
                }
            } else if (orderToUpdate.status === 'em produção' || orderToUpdate.status === 'em fila de produção' || orderToUpdate.status === 'enviado' || orderToUpdate.status === 'shipped' || orderToUpdate.status === 'concluída') {
                // Atualizar pedido que já está em produção, em fila ou enviado
                const orderIndex = customer.orders.findIndex(o => o.id === orderId);
                if (orderIndex !== -1) {
                    customer.orders[orderIndex].status = newStatus;
                    customer.orders[orderIndex].updatedAt = new Date();
                    orderUpdated = true;
                }
            }

            if (orderUpdated) {
                // Atualizar o cliente no Firebase
                await customerStore.updateCustomer(customer);

                // Atualizar o pedido na lista local
                const orderIndex = orders.value.findIndex(o => o.id === orderId);
                if (orderIndex !== -1) {
                    orders.value[orderIndex].status = newStatus;
                }
            }
        }
        // Para os status padrão, seguimos a lógica existente
        else if (newStatus === 'approved') {
            // Se o pedido está sendo aprovado, mover de pendingOrders para orders
            if (orderToUpdate.status === 'pending') {
                // Encontrar o pedido pendente
                const pendingOrderIndex = customer.pendingOrders.findIndex(o => o.id === orderId);
                if (pendingOrderIndex !== -1) {
                    // Obter o pedido pendente
                    const pendingOrder = customer.pendingOrders[pendingOrderIndex];

                    // Criar um novo pedido aprovado
                    const approvedOrder: IOrder = {
                        id: pendingOrder.id,
                        createdAt: pendingOrder.createdAt,
                        updatedAt: new Date(),
                        paymentId: pendingOrder.paymentId,
                        collectionId: pendingOrder.collectionId,
                        collectionStatus: 'approved',
                        paymentType: pendingOrder.paymentType,
                        externalReference: pendingOrder.externalReference,
                        merchantOrderId: pendingOrder.merchantOrderId,
                        preferenceId: pendingOrder.preferenceId,
                        siteId: pendingOrder.siteId,
                        processingMode: pendingOrder.processingMode,
                        merchantAccountId: pendingOrder.merchantAccountId,
                        customerId: pendingOrder.customerId || customer.id || '',
                        status: 'approved',
                        productId: pendingOrder.productId || '',
                        total: pendingOrder.total,
                        shipping: pendingOrder.shipping,
                        quantity: pendingOrder.quantity,
                        paymentProvider: pendingOrder.paymentProvider,
                        isTest: pendingOrder.isTest // Preservar a flag isTest
                    };

                    // Adicionar o pedido aprovado à lista de pedidos
                    customer.orders.push(approvedOrder);

                    // Remover o pedido pendente
                    customer.pendingOrders.splice(pendingOrderIndex, 1);

                    // Atualizar o cliente no Firebase
                    await customerStore.updateCustomer(customer);

                    orderUpdated = true;
                }
            } else if (orderToUpdate.status === 'rejected') {
                // Encontrar o pedido rejeitado
                const rejectedOrderIndex = customer.rejectedOrders.findIndex(o => o.id === orderId);
                if (rejectedOrderIndex !== -1) {
                    // Obter o pedido rejeitado
                    const rejectedOrder = customer.rejectedOrders[rejectedOrderIndex];

                    // Criar um novo pedido aprovado
                    const approvedOrder: IOrder = {
                        id: rejectedOrder.id,
                        createdAt: rejectedOrder.createdAt,
                        updatedAt: new Date(),
                        paymentId: rejectedOrder.paymentId,
                        collectionId: rejectedOrder.collectionId,
                        collectionStatus: 'approved',
                        paymentType: rejectedOrder.paymentType,
                        externalReference: rejectedOrder.externalReference,
                        merchantOrderId: rejectedOrder.merchantOrderId,
                        preferenceId: rejectedOrder.preferenceId,
                        siteId: rejectedOrder.siteId,
                        processingMode: rejectedOrder.processingMode,
                        merchantAccountId: rejectedOrder.merchantAccountId,
                        customerId: rejectedOrder.customerId || customer.id || '',
                        status: 'approved',
                        productId: rejectedOrder.productId || '',
                        total: rejectedOrder.total,
                        shipping: rejectedOrder.shipping,
                        quantity: rejectedOrder.quantity,
                        paymentProvider: rejectedOrder.paymentProvider,
                        isTest: rejectedOrder.isTest // Preservar a flag isTest
                    };

                    // Adicionar o pedido aprovado à lista de pedidos
                    customer.orders.push(approvedOrder);

                    // Remover o pedido rejeitado
                    customer.rejectedOrders.splice(rejectedOrderIndex, 1);

                    // Atualizar o cliente no Firebase
                    await customerStore.updateCustomer(customer);

                    orderUpdated = true;
                }
            }
        } else if (newStatus === 'rejected') {
            // Se o pedido está sendo rejeitado
            if (orderToUpdate.status === 'pending') {
                // Encontrar o pedido pendente
                const pendingOrderIndex = customer.pendingOrders.findIndex(o => o.id === orderId);
                if (pendingOrderIndex !== -1) {
                    // Obter o pedido pendente
                    const pendingOrder = customer.pendingOrders[pendingOrderIndex];

                    // Criar um novo pedido rejeitado
                    const rejectedOrder: IRejectedOrder = {
                        id: pendingOrder.id,
                        createdAt: pendingOrder.createdAt,
                        updatedAt: new Date(),
                        paymentId: pendingOrder.paymentId,
                        collectionId: pendingOrder.collectionId,
                        collectionStatus: 'rejected',
                        paymentType: pendingOrder.paymentType,
                        externalReference: pendingOrder.externalReference,
                        merchantOrderId: pendingOrder.merchantOrderId,
                        preferenceId: pendingOrder.preferenceId,
                        siteId: pendingOrder.siteId,
                        processingMode: pendingOrder.processingMode,
                        merchantAccountId: pendingOrder.merchantAccountId,
                        customerId: pendingOrder.customerId || customer.id || '',
                        status: 'rejected',
                        productId: pendingOrder.productId || '',
                        total: pendingOrder.total,
                        shipping: pendingOrder.shipping,
                        quantity: pendingOrder.quantity,
                        paymentProvider: pendingOrder.paymentProvider,
                        isTest: pendingOrder.isTest // Preservar a flag isTest
                    };

                    // Adicionar o pedido rejeitado à lista de pedidos rejeitados
                    customer.rejectedOrders.push(rejectedOrder);

                    // Remover o pedido pendente
                    customer.pendingOrders.splice(pendingOrderIndex, 1);

                    // Atualizar o cliente no Firebase
                    await customerStore.updateCustomer(customer);

                    orderUpdated = true;
                }
            } else if (orderToUpdate.status === 'approved') {
                // Encontrar o pedido aprovado
                const approvedOrderIndex = customer.orders.findIndex(o => o.id === orderId);
                if (approvedOrderIndex !== -1) {
                    // Obter o pedido aprovado
                    const approvedOrder = customer.orders[approvedOrderIndex];

                    // Criar um novo pedido rejeitado
                    const rejectedOrder: IRejectedOrder = {
                        id: approvedOrder.id,
                        createdAt: approvedOrder.createdAt,
                        updatedAt: new Date(),
                        paymentId: approvedOrder.paymentId,
                        collectionId: approvedOrder.collectionId,
                        collectionStatus: 'rejected',
                        paymentType: approvedOrder.paymentType,
                        externalReference: approvedOrder.externalReference,
                        merchantOrderId: approvedOrder.merchantOrderId,
                        preferenceId: approvedOrder.preferenceId,
                        siteId: approvedOrder.siteId,
                        processingMode: approvedOrder.processingMode,
                        merchantAccountId: approvedOrder.merchantAccountId,
                        customerId: approvedOrder.customerId,
                        status: 'rejected',
                        productId: approvedOrder.productId,
                        total: approvedOrder.total,
                        shipping: approvedOrder.shipping,
                        quantity: approvedOrder.quantity,
                        paymentProvider: approvedOrder.paymentProvider,
                        isTest: approvedOrder.isTest // Preservar a flag isTest
                    };

                    // Adicionar o pedido rejeitado à lista de pedidos rejeitados
                    customer.rejectedOrders.push(rejectedOrder);

                    // Remover o pedido aprovado
                    customer.orders.splice(approvedOrderIndex, 1);

                    // Atualizar o cliente no Firebase
                    await customerStore.updateCustomer(customer);

                    orderUpdated = true;
                }
            }
        }

        if (!orderUpdated) {
            throw new Error('Não foi possível atualizar o status do pedido');
        }

        // Atualizar o pedido na lista local
        const orderIndex = orders.value.findIndex(o => o.id === orderId);
        if (orderIndex !== -1) {
            orders.value[orderIndex].status = newStatus;
        }

        // Se o modal estiver aberto, atualizar o pedido selecionado
        if (selectedOrder.value && selectedOrder.value.id === orderId) {
            selectedOrder.value.status = newStatus;
        }

        // Fechar o modal se estiver aberto
        if (showOrderDetailsModal.value) {
            showOrderDetailsModal.value = false;
        }

        // Recarregar os pedidos para atualizar a lista
        await loadOrders();
    } catch (e) {
        console.error('Erro ao atualizar status do pedido:', e);
        error.value = 'Ocorreu um erro ao atualizar o status do pedido. Por favor, tente novamente.';
    } finally {
        loading.value = false;
    }
};

// Funções para exclusão de pedidos
const confirmDeleteOrder = (order: Order | null) => {
    if (!order) return;
    orderToDelete.value = order;
    showDeleteModal.value = true;
};

const cancelDeleteOrder = () => {
    orderToDelete.value = null;
    showDeleteModal.value = false;
};

const deleteOrder = async () => {
    if (!orderToDelete.value) return;

    deleting.value = true;

    try {
        console.log(`🔍 [Frontend] Tentando excluir pedido:`, {
            orderId: orderToDelete.value.id,
            customerId: orderToDelete.value.customerId,
            status: orderToDelete.value.status,
            paymentId: orderToDelete.value.paymentId
        });

        // Forçar atualização do cache de clientes antes de tentar excluir
        console.log(`🔄 [Frontend] Forçando atualização do cache de clientes...`);
        const customerStore = useCustomerStore();

        // Forçar refresh do cache usando parâmetro de query
        await $fetch('/api/customers?refresh=true');
        await customerStore.fetchAllCustomers();

        // Verificar se o cliente ainda existe após a atualização
        const customer = customerStore.getCustomerById(orderToDelete.value.customerId);
        if (!customer) {
            throw new Error(`Cliente ${orderToDelete.value.customerId} não encontrado após atualização do cache`);
        }

        console.log(`🔍 [Frontend] Cliente encontrado:`, {
            customerId: customer.id,
            ordersCount: customer.orders?.length || 0,
            pendingOrdersCount: customer.pendingOrders?.length || 0,
            rejectedOrdersCount: customer.rejectedOrders?.length || 0
        });

        // Chamar a API para excluir o pedido
        const response = await $fetch(`/api/customers/${orderToDelete.value.customerId}/orders/${orderToDelete.value.id}`, {
            method: 'DELETE'
        });

        console.log(`✅ [Frontend] Resposta da API:`, response);

        if (response.success) {
            console.log(`✅ Pedido ${orderToDelete.value.id} excluído com sucesso`);

            // Fechar o modal de detalhes se estiver aberto
            if (showOrderDetailsModal.value && selectedOrder.value?.id === orderToDelete.value.id) {
                showOrderDetailsModal.value = false;
                selectedOrder.value = null;
            }

            // Fechar o modal de confirmação
            cancelDeleteOrder();

            // Recarregar todos os dados para garantir consistência
            console.log('🔄 [Frontend] Recarregando dados após exclusão...');
            await loadOrders(true); // Forçar refresh do cache
        } else {
            throw new Error('Falha ao excluir pedido');
        }
    } catch (e: any) {
        console.error('Erro ao excluir pedido:', e);
        error.value = 'Ocorreu um erro ao excluir o pedido. Por favor, tente novamente.';
    } finally {
        deleting.value = false;
    }
};

// Lifecycle hooks
onMounted(() => {
    loadOrders();
});
</script>

<style scoped>
/* Garantir que a coluna de ações fique fixa no lado direito */
.sticky {
    position: sticky;
    z-index: 10;
}

/* Adicionar sombra para destacar a coluna fixa */
.sticky.right-0 {
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
}

/* Garantir que o fundo seja sólido para evitar sobreposição */
thead .sticky {
    z-index: 20;
}

/* Melhorar a aparência em dispositivos móveis */
@media (max-width: 768px) {
    .sticky.right-0 {
        box-shadow: -1px 0 2px rgba(0, 0, 0, 0.1);
    }
}
</style>
